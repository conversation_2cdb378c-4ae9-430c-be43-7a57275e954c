import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import ApproverManagement from './Components/ApproverManagement';

const theme = createTheme({
  palette: {
    primary: {
      main: '#2EC0CB',
      light: '#5ED3DD',
      dark: '#208B94',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#2c3e50',
    },
    background: {
      default: '#f8f9fa',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif',
    h1: {
      fontFamily: '"Inter", sans-serif',
    },
    h2: {
      fontFamily: '"Inter", sans-serif',
    },
    h3: {
      fontFamily: '"Inter", sans-serif',
    },
    h4: {
      fontFamily: '"Inter", sans-serif',
    },
    h5: {
      fontFamily: '"Inter", sans-serif',
    },
    h6: {
      fontFamily: '"Inter", sans-serif',
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          fontFamily: '"Inter", sans-serif',
          fontWeight: 500,
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiInputLabel-root': {
            fontFamily: '"Inter", sans-serif',
          },
          '& .MuiInputBase-input': {
            fontFamily: '"Inter", sans-serif',
          },
        },
      },
    },
    MuiAutocomplete: {
      styleOverrides: {
        root: {
          '& .MuiInputLabel-root': {
            fontFamily: '"Inter", sans-serif',
          },
          '& .MuiInputBase-input': {
            fontFamily: '"Inter", sans-serif',
          },
        },
      },
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <ApproverManagement />
    </ThemeProvider>
  );
}

export default App;
