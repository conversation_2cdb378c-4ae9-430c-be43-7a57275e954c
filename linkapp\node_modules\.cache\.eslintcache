[{"C:\\Users\\<USER>\\Documents\\React\\TLT_Links\\linkapp\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\React\\TLT_Links\\linkapp\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Documents\\React\\TLT_Links\\linkapp\\src\\App.js": "3", "C:\\Users\\<USER>\\Documents\\React\\TLT_Links\\linkapp\\src\\Components\\ApproverManagement.js": "4"}, {"size": 535, "mtime": 1751970213666, "results": "5", "hashOfConfig": "6"}, {"size": 362, "mtime": 1751970214079, "results": "7", "hashOfConfig": "6"}, {"size": 1904, "mtime": 1751977553978, "results": "8", "hashOfConfig": "6"}, {"size": 19339, "mtime": 1752132810368, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "izjeh6", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\React\\TLT_Links\\linkapp\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\React\\TLT_Links\\linkapp\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\React\\TLT_Links\\linkapp\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\React\\TLT_Links\\linkapp\\src\\Components\\ApproverManagement.js", ["22", "23", "24", "25", "26", "27"], [], {"ruleId": "28", "severity": 1, "message": "29", "line": 7, "column": 3, "nodeType": "30", "messageId": "31", "endLine": 7, "endColumn": 9}, {"ruleId": "28", "severity": 1, "message": "32", "line": 8, "column": 3, "nodeType": "30", "messageId": "31", "endLine": 8, "endColumn": 11}, {"ruleId": "28", "severity": 1, "message": "33", "line": 9, "column": 3, "nodeType": "30", "messageId": "31", "endLine": 9, "endColumn": 14}, {"ruleId": "28", "severity": 1, "message": "34", "line": 10, "column": 3, "nodeType": "30", "messageId": "31", "endLine": 10, "endColumn": 13}, {"ruleId": "28", "severity": 1, "message": "35", "line": 20, "column": 3, "nodeType": "30", "messageId": "31", "endLine": 20, "endColumn": 7}, {"ruleId": "28", "severity": 1, "message": "36", "line": 21, "column": 3, "nodeType": "30", "messageId": "31", "endLine": 21, "endColumn": 13}, "no-unused-vars", "'Select' is defined but never used.", "Identifier", "unusedVar", "'MenuItem' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Chip' is defined but never used.", "'IconButton' is defined but never used."]