import React, { useState } from 'react';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Grid,
  Box,
  Chip,
  IconButton,
  Alert,
  Autocomplete,
  Card,
  CardContent,
  CardActions,
  ToggleButton,
  ToggleButtonGroup,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  Delete as DeleteIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  ViewStream as CardViewIcon,
  TableChart as TableViewIcon
} from '@mui/icons-material';

const ApproverManagement = () => {
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [associateApprover, setAssociateApprover] = useState(null);
  const [backupApprover, setBackupApprover] = useState(null);
  const [assignments, setAssignments] = useState([]);
  const [notification, setNotification] = useState({ type: '', message: '' });
  const [viewMode, setViewMode] = useState('table'); // 'table', 'grid', 'card', 'list'

  // Sample users data (in real app, this would come from an API)
  const availableUsers = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', department: 'Engineering' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', department: 'Marketing' },
    { id: 3, name: 'Mike Johnson', email: '<EMAIL>', department: 'Sales' },
    { id: 4, name: 'Sarah Wilson', email: '<EMAIL>', department: 'HR' },
    { id: 5, name: 'David Brown', email: '<EMAIL>', department: 'Finance' },
    { id: 6, name: 'Lisa Davis', email: '<EMAIL>', department: 'Operations' },
  ];

  const availableApprovers = [
    { id: 101, name: 'Manager A', email: '<EMAIL>', role: 'Team Lead' },
    { id: 102, name: 'Manager B', email: '<EMAIL>', role: 'Department Head' },
    { id: 103, name: 'Manager C', email: '<EMAIL>', role: 'Senior Manager' },
    { id: 104, name: 'Manager D', email: '<EMAIL>', role: 'Director' },
  ];

  const handleAssignApprovers = () => {
    if (!selectedUser || !associateApprover) {
      setNotification({ type: 'error', message: 'Please select a user and at least an associate approver' });
      return;
    }

    if (associateApprover?.id === backupApprover?.id) {
      setNotification({ type: 'error', message: 'Associate and backup approvers must be different' });
      return;
    }

    const newAssignment = {
      id: Date.now(),
      user: selectedUser,
      associateApprover: associateApprover,
      backupApprover: backupApprover,
      assignedDate: new Date().toLocaleDateString()
    };

    setAssignments([...assignments, newAssignment]);
    
    // Reset form
    setSelectedUser(null);
    setAssociateApprover(null);
    setBackupApprover(null);
    
    setNotification({ type: 'success', message: 'Approvers assigned successfully!' });
  };

  const handleRemoveAssignment = (id) => {
    setAssignments(assignments.filter(assignment => assignment.id !== id));
  };

  const handleViewChange = (event, newView) => {
    if (newView !== null) {
      setViewMode(newView);
    }
  };

  const renderGridView = () => (
    <Grid container spacing={3} sx={{ mt: 2 }}>
      {assignments.map(assignment => (
        <Grid item xs={12} sm={6} md={4} key={assignment.id}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontFamily: '"Inter", sans-serif' }}>
                {assignment.user.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {assignment.user.email}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Department: {assignment.user.department}
              </Typography>
              <Typography variant="body2" sx={{ mt: 2, fontWeight: 500 }}>
                Associate: {assignment.associateApprover.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {assignment.associateApprover.role}
              </Typography>
              {assignment.backupApprover && (
                <>
                  <Typography variant="body2" sx={{ mt: 1, fontWeight: 500 }}>
                    Backup: {assignment.backupApprover.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {assignment.backupApprover.role}
                  </Typography>
                </>
              )}
              <Typography variant="caption" sx={{ display: 'block', mt: 2 }}>
                Assigned: {assignment.assignedDate}
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                onClick={() => handleRemoveAssignment(assignment.id)}
                color="error"
                size="small"
                startIcon={<DeleteIcon />}
                sx={{
                  fontFamily: '"Inter", sans-serif',
                  textTransform: 'none',
                }}
              >
                Delete
              </Button>
            </CardActions>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderCardView = () => (
    <Box sx={{ mt: 2 }}>
      {assignments.map((assignment, index) => (
        <Card key={assignment.id} elevation={1} sx={{ mb: 2 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box sx={{ flex: 1 }}>
                <Typography variant="h6" sx={{ fontFamily: '"Inter", sans-serif', mb: 1 }}>
                  {assignment.user.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {assignment.user.email} • {assignment.user.department}
                </Typography>
                <Box sx={{ display: 'flex', gap: 4, mt: 2 }}>
                  <Box>
                    <Typography variant="body2" fontWeight={600}>
                      Associate Approver
                    </Typography>
                    <Typography variant="body2">
                      {assignment.associateApprover.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {assignment.associateApprover.role}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" fontWeight={600}>
                      Backup Approver
                    </Typography>
                    {assignment.backupApprover ? (
                      <>
                        <Typography variant="body2">
                          {assignment.backupApprover.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {assignment.backupApprover.role}
                        </Typography>
                      </>
                    ) : (
                      <Typography variant="body2" color="text.secondary" fontStyle="italic">
                        No backup assigned
                      </Typography>
                    )}
                  </Box>
                  <Box>
                    <Typography variant="body2" fontWeight={600}>
                      Assigned Date
                    </Typography>
                    <Typography variant="body2">
                      {assignment.assignedDate}
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Button
                onClick={() => handleRemoveAssignment(assignment.id)}
                color="error"
                size="small"
                startIcon={<DeleteIcon />}
                sx={{
                  fontFamily: '"Inter", sans-serif',
                  textTransform: 'none',
                }}
              >
                Delete
              </Button>
            </Box>
          </CardContent>
        </Card>
      ))}
    </Box>
  );

  const renderListView = () => (
    <List sx={{ mt: 2 }}>
      {assignments.map((assignment, index) => (
        <React.Fragment key={assignment.id}>
          <ListItem>
            <ListItemText
              primary={
                <Typography variant="h6" sx={{ fontFamily: '"Inter", sans-serif' }}>
                  {assignment.user.name}
                </Typography>
              }
              secondary={
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    {assignment.user.email} • {assignment.user.department}
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Associate: {assignment.associateApprover.name} ({assignment.associateApprover.role})
                  </Typography>
                  {assignment.backupApprover && (
                    <Typography variant="body2">
                      Backup: {assignment.backupApprover.name} ({assignment.backupApprover.role})
                    </Typography>
                  )}
                  <Typography variant="caption" color="text.secondary">
                    Assigned: {assignment.assignedDate}
                  </Typography>
                </Box>
              }
            />
            <ListItemSecondaryAction>
              <Button
                onClick={() => handleRemoveAssignment(assignment.id)}
                color="error"
                size="small"
                startIcon={<DeleteIcon />}
                sx={{
                  fontFamily: '"Inter", sans-serif',
                  textTransform: 'none',
                }}
              >
                Delete
              </Button>
            </ListItemSecondaryAction>
          </ListItem>
          {index < assignments.length - 1 && <Divider />}
        </React.Fragment>
      ))}
    </List>
  );

  const renderTableView = () => (
    <TableContainer sx={{ mt: 3 }}>
      <Table>
        <TableHead>
          <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
            <TableCell sx={{ fontWeight: 600 }}>User</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Department</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Associate Approver</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Backup Approver</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Assigned Date</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {assignments.map(assignment => (
            <TableRow key={assignment.id} hover>
              <TableCell>
                <Box>
                  <Typography variant="body2" fontWeight={600}>
                    {assignment.user.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {assignment.user.email}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>{assignment.user.department}</TableCell>
              <TableCell>
                <Box>
                  <Typography variant="body2" fontWeight={600}>
                    {assignment.associateApprover.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {assignment.associateApprover.role}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>
                {assignment.backupApprover ? (
                  <Box>
                    <Typography variant="body2" fontWeight={600}>
                      {assignment.backupApprover.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {assignment.backupApprover.role}
                    </Typography>
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary" fontStyle="italic">
                    No backup assigned
                  </Typography>
                )}
              </TableCell>
              <TableCell>{assignment.assignedDate}</TableCell>
              <TableCell>
                <Button
                  onClick={() => handleRemoveAssignment(assignment.id)}
                  color="error"
                  size="small"
                  variant="contained"
                  startIcon={<DeleteIcon />}
                  sx={{
                    fontFamily: '"Inter", sans-serif',
                    fontWeight: 500,
                    textTransform: 'none',
                  }}
                >
                  Delete
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box textAlign="center" mb={4}>
        <Typography 
          variant="h3" 
          component="h1" 
          gutterBottom 
          sx={{ 
            fontWeight: 600, 
            color: '#2EC0CB',
            fontFamily: '"Inter", sans-serif',
          }}
        >
          Associate Approver and Backup Approver To User
        </Typography>
        <Typography 
          variant="h6" 
          color="text.secondary" 
          sx={{ 
            maxWidth: 600, 
            mx: 'auto',
            fontFamily: '"Inter", sans-serif',
          }}
        >
          Assign associate and backup approvers to users for workflow management
        </Typography>
      </Box>

      {/* Notification */}
      {notification.message && (
        <Alert 
          severity={notification.type} 
          onClose={() => setNotification({ type: '', message: '' })}
          sx={{ mb: 3 }}
        >
          {notification.message}
        </Alert>
      )}

      <Grid container spacing={4}>
        {/* Assignment Form */}
        <Grid item xs={12} md={4}>
          <Paper elevation={3} sx={{ p: 3 }}>
            <Typography 
              variant="h5" 
              gutterBottom 
              sx={{ 
                borderBottom: 2, 
                borderColor: '#2EC0CB', 
                pb: 1,
                fontFamily: '"Inter", sans-serif',
                fontWeight: 600,
                color: '#2EC0CB'
              }}
            >
              Assign Approvers
            </Typography>
            
            <Box component="form" sx={{ mt: 3 }}>
              <Autocomplete
                options={availableUsers}
                getOptionLabel={(option) => option.name}
                value={selectedUser}
                onChange={(event, newValue) => setSelectedUser(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select User"
                    margin="normal"
                    fullWidth
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: '"Inter", sans-serif',
                      },
                      '& .MuiInputBase-input': {
                        fontFamily: '"Inter", sans-serif',
                      },
                    }}
                  />
                )}
                sx={{ mb: 2 }}
              />

              <Autocomplete
                options={availableApprovers}
                getOptionLabel={(option) => `${option.name} - ${option.role}`}
                value={associateApprover}
                onChange={(event, newValue) => setAssociateApprover(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Associate Approver *"
                    margin="normal"
                    fullWidth
                    required
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: '"Inter", sans-serif',
                      },
                      '& .MuiInputBase-input': {
                        fontFamily: '"Inter", sans-serif',
                      },
                    }}
                  />
                )}
                sx={{ mb: 2 }}
              />

              <Autocomplete
                options={availableApprovers.filter(approver => approver.id !== associateApprover?.id)}
                getOptionLabel={(option) => `${option.name} - ${option.role}`}
                value={backupApprover}
                onChange={(event, newValue) => setBackupApprover(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Backup Approver (Optional)"
                    margin="normal"
                    fullWidth
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: '"Inter", sans-serif',
                      },
                      '& .MuiInputBase-input': {
                        fontFamily: '"Inter", sans-serif',
                      },
                    }}
                  />
                )}
                sx={{ mb: 2 }}
              />

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleAssignApprovers}
                startIcon={<PersonAddIcon />}
                sx={{ 
                  mt: 3, 
                  py: 1.5,
                  fontFamily: '"Inter", sans-serif',
                  fontWeight: 500,
                  backgroundColor: '#2EC0CB',
                  '&:hover': {
                    backgroundColor: '#208B94',
                  },
                }}
              >
                Assign Approvers
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Current Assignments */}
        <Grid item xs={12} md={8}>
          <Paper elevation={3} sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography 
                variant="h5" 
                sx={{ 
                  borderBottom: 2, 
                  borderColor: '#2EC0CB', 
                  pb: 1,
                  fontFamily: '"Inter", sans-serif',
                  fontWeight: 600,
                  color: '#2EC0CB'
                }}
              >
                Current Assignments ({assignments.length})
              </Typography>
              
              <ToggleButtonGroup
                value={viewMode}
                exclusive
                onChange={handleViewChange}
                size="small"
                sx={{
                  '& .MuiToggleButton-root': {
                    fontFamily: '"Inter", sans-serif',
                    border: '1px solid #2EC0CB',
                    color: '#2EC0CB',
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: 1,
                    textTransform: 'none',
                    px: 2,
                    py: 1,
                    '&.Mui-selected': {
                      backgroundColor: '#2EC0CB',
                      color: 'white',
                      '&:hover': {
                        backgroundColor: '#208B94',
                      },
                    },
                    '&:hover': {
                      backgroundColor: 'rgba(46, 192, 203, 0.1)',
                    },
                  },
                }}
              >
                <ToggleButton value="table" title="Table View">
                  <TableViewIcon fontSize="small" />
                  <Typography variant="caption" sx={{ ml: 0.5 }}>Table</Typography>
                </ToggleButton>
                <ToggleButton value="grid" title="Grid View">
                  <GridViewIcon fontSize="small" />
                  <Typography variant="caption" sx={{ ml: 0.5 }}>Grid</Typography>
                </ToggleButton>
                <ToggleButton value="card" title="Card View">
                  <CardViewIcon fontSize="small" />
                  <Typography variant="caption" sx={{ ml: 0.5 }}>Card</Typography>
                </ToggleButton>
                <ToggleButton value="list" title="List View">
                  <ListViewIcon fontSize="small" />
                  <Typography variant="caption" sx={{ ml: 0.5 }}>List</Typography>
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>
            
            {assignments.length === 0 ? (
              <Box textAlign="center" py={6}>
                <Typography variant="h6" color="text.secondary">
                  No approver assignments yet. Create your first assignment using the form.
                </Typography>
              </Box>
            ) : (
              <Box>
                {viewMode === 'table' && renderTableView()}
                {viewMode === 'grid' && renderGridView()}
                {viewMode === 'card' && renderCardView()}
                {viewMode === 'list' && renderListView()}
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ApproverManagement;
