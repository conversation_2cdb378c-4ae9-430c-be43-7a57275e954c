import React, { useState } from 'react';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Grid,
  Box,
  Chip,
  IconButton,
  Alert,
  Autocomplete,
  Card,
  CardContent,
  CardActions,
  ToggleButton,
  ToggleButtonGroup,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  Delete as DeleteIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  ViewStream as CardViewIcon,
  TableChart as TableViewIcon
} from '@mui/icons-material';
import './ApproverManagement.css';

const ApproverManagement = () => {
  const [selectedUser, setSelectedUser] = useState(null);
  const [associateApprover, setAssociateApprover] = useState(null);
  const [backupApprover, setBackupApprover] = useState(null);
  const [assignments, setAssignments] = useState([]);
  const [notification, setNotification] = useState({ type: '', message: '' });
  const [viewMode, setViewMode] = useState('table'); // 'table', 'grid', 'card', 'list'

  // Sample users data (in real app, this would come from an API)
  const availableUsers = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', department: 'Engineering' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', department: 'Marketing' },
    { id: 3, name: 'Mike Johnson', email: '<EMAIL>', department: 'Sales' },
    { id: 4, name: 'Sarah Wilson', email: '<EMAIL>', department: 'HR' },
    { id: 5, name: 'David Brown', email: '<EMAIL>', department: 'Finance' },
    { id: 6, name: 'Lisa Davis', email: '<EMAIL>', department: 'Operations' },
  ];

  const availableApprovers = [
    { id: 101, name: 'Manager A', email: '<EMAIL>', role: 'Team Lead' },
    { id: 102, name: 'Manager B', email: '<EMAIL>', role: 'Department Head' },
    { id: 103, name: 'Manager C', email: '<EMAIL>', role: 'Senior Manager' },
    { id: 104, name: 'Manager D', email: '<EMAIL>', role: 'Director' },
  ];

  const handleAssignApprovers = () => {
    if (!selectedUser || !associateApprover) {
      setNotification({ type: 'error', message: 'Please select a user and at least an associate approver' });
      return;
    }

    if (associateApprover?.id === backupApprover?.id) {
      setNotification({ type: 'error', message: 'Associate and backup approvers must be different' });
      return;
    }

    const newAssignment = {
      id: Date.now(),
      user: selectedUser,
      associateApprover: associateApprover,
      backupApprover: backupApprover,
      assignedDate: new Date().toLocaleDateString()
    };

    setAssignments([...assignments, newAssignment]);
    
    // Reset form
    setSelectedUser(null);
    setAssociateApprover(null);
    setBackupApprover(null);
    
    setNotification({ type: 'success', message: 'Approvers assigned successfully!' });
  };

  const handleRemoveAssignment = (id) => {
    setAssignments(assignments.filter(assignment => assignment.id !== id));
  };

  const handleViewChange = (event, newView) => {
    if (newView !== null) {
      setViewMode(newView);
    }
  };

  const renderGridView = () => (
    <Grid container spacing={3} className="grid-container">
      {assignments.map(assignment => (
        <Grid item xs={12} sm={6} md={4} key={assignment.id}>
          <Card className="grid-card">
            <CardContent className="grid-card-content">
              <Typography variant="h6" gutterBottom className="user-name">
                {assignment.user.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {assignment.user.email}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Department: {assignment.user.department}
              </Typography>
              <Typography variant="body2" className="associate-text">
                Associate: {assignment.associateApprover.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {assignment.associateApprover.role}
              </Typography>
              {assignment.backupApprover && (
                <>
                  <Typography variant="body2" className="backup-text">
                    Backup: {assignment.backupApprover.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {assignment.backupApprover.role}
                  </Typography>
                </>
              )}
              <Typography variant="caption" className="assigned-date">
                Assigned: {assignment.assignedDate}
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                onClick={() => handleRemoveAssignment(assignment.id)}
                color="error"
                size="small"
                startIcon={<DeleteIcon />}
                className="grid-delete-btn"
              >
                Delete
              </Button>
            </CardActions>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderCardView = () => (
    <Box className="card-container">
      {assignments.map((assignment) => (
        <Card key={assignment.id} elevation={1} className="card-item">
          <CardContent>
            <Box className="card-content-flex">
              <Box className="card-content-main">
                <Typography variant="h6" className="card-user-name">
                  {assignment.user.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {assignment.user.email} • {assignment.user.department}
                </Typography>
                <Box className="card-approvers-box">
                  <Box>
                    <Typography variant="body2" fontWeight={600}>
                      Associate Approver
                    </Typography>
                    <Typography variant="body2">
                      {assignment.associateApprover.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {assignment.associateApprover.role}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" fontWeight={600}>
                      Backup Approver
                    </Typography>
                    {assignment.backupApprover ? (
                      <>
                        <Typography variant="body2">
                          {assignment.backupApprover.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {assignment.backupApprover.role}
                        </Typography>
                      </>
                    ) : (
                      <Typography variant="body2" color="text.secondary" fontStyle="italic">
                        No backup assigned
                      </Typography>
                    )}
                  </Box>
                  <Box>
                    <Typography variant="body2" fontWeight={600}>
                      Assigned Date
                    </Typography>
                    <Typography variant="body2">
                      {assignment.assignedDate}
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Button
                onClick={() => handleRemoveAssignment(assignment.id)}
                color="error"
                size="small"
                startIcon={<DeleteIcon />}
                className="card-delete-btn"
              >
                Delete
              </Button>
            </Box>
          </CardContent>
        </Card>
      ))}
    </Box>
  );

  const renderListView = () => (
    <List className="list-container">
      {assignments.map((assignment, index) => (
        <React.Fragment key={assignment.id}>
          <ListItem>
            <ListItemText
              primary={
                <Typography variant="h6" className="list-user-name">
                  {assignment.user.name}
                </Typography>
              }
              secondary={
                <Box className="list-user-info">
                  <Typography variant="body2" color="text.secondary">
                    {assignment.user.email} • {assignment.user.department}
                  </Typography>
                  <Typography variant="body2">
                    Associate: {assignment.associateApprover.name} ({assignment.associateApprover.role})
                  </Typography>
                  {assignment.backupApprover && (
                    <Typography variant="body2">
                      Backup: {assignment.backupApprover.name} ({assignment.backupApprover.role})
                    </Typography>
                  )}
                  <Typography variant="caption" color="text.secondary">
                    Assigned: {assignment.assignedDate}
                  </Typography>
                </Box>
              }
            />
            <ListItemSecondaryAction>
              <Button
                onClick={() => handleRemoveAssignment(assignment.id)}
                color="error"
                size="small"
                startIcon={<DeleteIcon />}
                className="list-delete-btn"
              >
                Delete
              </Button>
            </ListItemSecondaryAction>
          </ListItem>
          {index < assignments.length - 1 && <Divider />}
        </React.Fragment>
      ))}
    </List>
  );

  const renderTableView = () => (
    <TableContainer className="table-container">
      <Table>
        <TableHead>
          <TableRow className="table-header-row">
            <TableCell className="table-header-cell">User</TableCell>
            <TableCell className="table-header-cell">Department</TableCell>
            <TableCell className="table-header-cell">Associate Approver</TableCell>
            <TableCell className="table-header-cell">Backup Approver</TableCell>
            <TableCell className="table-header-cell">Assigned Date</TableCell>
            <TableCell className="table-header-cell">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {assignments.map(assignment => (
            <TableRow key={assignment.id} hover>
              <TableCell>
                <Box>
                  <Typography variant="body2" fontWeight={600}>
                    {assignment.user.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {assignment.user.email}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>{assignment.user.department}</TableCell>
              <TableCell>
                <Box>
                  <Typography variant="body2" fontWeight={600}>
                    {assignment.associateApprover.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {assignment.associateApprover.role}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>
                {assignment.backupApprover ? (
                  <Box>
                    <Typography variant="body2" fontWeight={600}>
                      {assignment.backupApprover.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {assignment.backupApprover.role}
                    </Typography>
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary" fontStyle="italic">
                    No backup assigned
                  </Typography>
                )}
              </TableCell>
              <TableCell>{assignment.assignedDate}</TableCell>
              <TableCell>
                <Button
                  onClick={() => handleRemoveAssignment(assignment.id)}
                  color="error"
                  size="small"
                  variant="contained"
                  startIcon={<DeleteIcon />}
                  className="table-delete-btn"
                >
                  Delete
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Container maxWidth="xl" className="approver-container">
      {/* Header */}
      <Box className="header-box">
        <Typography
          variant="h3"
          component="h1"
          gutterBottom
          className="main-title"
        >
          Associate Approver and Backup Approver To User
        </Typography>
        <Typography
          variant="h6"
          color="text.secondary"
          className="subtitle"
        >
          Assign associate and backup approvers to users for workflow management
        </Typography>
      </Box>

      {/* Notification */}
      {notification.message && (
        <Alert
          severity={notification.type}
          onClose={() => setNotification({ type: '', message: '' })}
          className="notification-alert"
        >
          {notification.message}
        </Alert>
      )}

      <Grid container spacing={4}>
        {/* Assignment Form */}
        <Grid item xs={12} md={4}>
          <Paper elevation={3} className="form-paper">
            <Typography
              variant="h5"
              gutterBottom
              className="form-title"
            >
              Assign Approvers
            </Typography>

            <Box component="form" className="form-box">
              <Autocomplete
                options={availableUsers}
                getOptionLabel={(option) => option.name}
                value={selectedUser}
                onChange={(_, newValue) => setSelectedUser(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select User"
                    margin="normal"
                    fullWidth
                    className="form-textfield"
                  />
                )}
                className="form-autocomplete"
              />

              <Autocomplete
                options={availableApprovers}
                getOptionLabel={(option) => `${option.name} - ${option.role}`}
                value={associateApprover}
                onChange={(_, newValue) => setAssociateApprover(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Associate Approver *"
                    margin="normal"
                    fullWidth
                    required
                    className="form-textfield"
                  />
                )}
                className="form-autocomplete"
              />

              <Autocomplete
                options={availableApprovers.filter(approver => approver.id !== associateApprover?.id)}
                getOptionLabel={(option) => `${option.name} - ${option.role}`}
                value={backupApprover}
                onChange={(_, newValue) => setBackupApprover(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Backup Approver (Optional)"
                    margin="normal"
                    fullWidth
                    className="form-textfield"
                  />
                )}
                className="form-autocomplete"
              />

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleAssignApprovers}
                startIcon={<PersonAddIcon />}
                className="assign-button"
              >
                Assign Approvers
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Current Assignments */}
        <Grid item xs={12} md={8}>
          <Paper elevation={3} className="assignments-paper">
            <Box className="assignments-header">
              <Typography
                variant="h5"
                className="assignments-title"
              >
                Current Assignments ({assignments.length})
              </Typography>

              <ToggleButtonGroup
                value={viewMode}
                exclusive
                onChange={handleViewChange}
                size="small"
                className="view-toggle-group"
              >
                <ToggleButton value="table" title="Table View">
                  <TableViewIcon fontSize="small" />
                  <Typography variant="caption" className="toggle-button-text">Table</Typography>
                </ToggleButton>
                <ToggleButton value="grid" title="Grid View">
                  <GridViewIcon fontSize="small" />
                  <Typography variant="caption" className="toggle-button-text">Grid</Typography>
                </ToggleButton>
                <ToggleButton value="card" title="Card View">
                  <CardViewIcon fontSize="small" />
                  <Typography variant="caption" className="toggle-button-text">Card</Typography>
                </ToggleButton>
                <ToggleButton value="list" title="List View">
                  <ListViewIcon fontSize="small" />
                  <Typography variant="caption" className="toggle-button-text">List</Typography>
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>

            {assignments.length === 0 ? (
              <Box className="empty-state">
                <Typography variant="h6" color="text.secondary">
                  No approver assignments yet. Create your first assignment using the form.
                </Typography>
              </Box>
            ) : (
              <Box>
                {viewMode === 'table' && renderTableView()}
                {viewMode === 'grid' && renderGridView()}
                {viewMode === 'card' && renderCardView()}
                {viewMode === 'list' && renderListView()}
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ApproverManagement;
