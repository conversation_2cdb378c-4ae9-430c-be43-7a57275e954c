{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\React\\\\TLT_Links\\\\linkapp\\\\src\\\\Components\\\\ApproverManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Paper, Typography, TextField, Select, MenuItem, FormControl, InputLabel, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Grid, Box, Chip, IconButton, Alert, Autocomplete, Card, CardContent, CardActions, ToggleButton, ToggleButtonGroup, List, ListItem, ListItemText, ListItemSecondaryAction, Divider } from '@mui/material';\nimport { PersonAdd as PersonAddIcon, Delete as DeleteIcon, ViewModule as GridViewIcon, ViewList as ListViewIcon, ViewStream as CardViewIcon, TableChart as TableViewIcon } from '@mui/icons-material';\nimport './ApproverManagement.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ApproverManagement = () => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [associateApprover, setAssociateApprover] = useState(null);\n  const [backupApprover, setBackupApprover] = useState(null);\n  const [assignments, setAssignments] = useState([]);\n  const [notification, setNotification] = useState({\n    type: '',\n    message: ''\n  });\n  const [viewMode, setViewMode] = useState('table'); // 'table', 'grid', 'card', 'list'\n\n  // Sample users data (in real app, this would come from an API)\n  const availableUsers = [{\n    id: 1,\n    name: 'John Doe',\n    email: '<EMAIL>',\n    department: 'Engineering'\n  }, {\n    id: 2,\n    name: 'Jane Smith',\n    email: '<EMAIL>',\n    department: 'Marketing'\n  }, {\n    id: 3,\n    name: 'Mike Johnson',\n    email: '<EMAIL>',\n    department: 'Sales'\n  }, {\n    id: 4,\n    name: 'Sarah Wilson',\n    email: '<EMAIL>',\n    department: 'HR'\n  }, {\n    id: 5,\n    name: 'David Brown',\n    email: '<EMAIL>',\n    department: 'Finance'\n  }, {\n    id: 6,\n    name: 'Lisa Davis',\n    email: '<EMAIL>',\n    department: 'Operations'\n  }];\n  const availableApprovers = [{\n    id: 101,\n    name: 'Manager A',\n    email: '<EMAIL>',\n    role: 'Team Lead'\n  }, {\n    id: 102,\n    name: 'Manager B',\n    email: '<EMAIL>',\n    role: 'Department Head'\n  }, {\n    id: 103,\n    name: 'Manager C',\n    email: '<EMAIL>',\n    role: 'Senior Manager'\n  }, {\n    id: 104,\n    name: 'Manager D',\n    email: '<EMAIL>',\n    role: 'Director'\n  }];\n  const handleAssignApprovers = () => {\n    if (!selectedUser || !associateApprover) {\n      setNotification({\n        type: 'error',\n        message: 'Please select a user and at least an associate approver'\n      });\n      return;\n    }\n    if ((associateApprover === null || associateApprover === void 0 ? void 0 : associateApprover.id) === (backupApprover === null || backupApprover === void 0 ? void 0 : backupApprover.id)) {\n      setNotification({\n        type: 'error',\n        message: 'Associate and backup approvers must be different'\n      });\n      return;\n    }\n    const newAssignment = {\n      id: Date.now(),\n      user: selectedUser,\n      associateApprover: associateApprover,\n      backupApprover: backupApprover,\n      assignedDate: new Date().toLocaleDateString()\n    };\n    setAssignments([...assignments, newAssignment]);\n\n    // Reset form\n    setSelectedUser(null);\n    setAssociateApprover(null);\n    setBackupApprover(null);\n    setNotification({\n      type: 'success',\n      message: 'Approvers assigned successfully!'\n    });\n  };\n  const handleRemoveAssignment = id => {\n    setAssignments(assignments.filter(assignment => assignment.id !== id));\n  };\n  const handleViewChange = (event, newView) => {\n    if (newView !== null) {\n      setViewMode(newView);\n    }\n  };\n  const renderGridView = () => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    className: \"grid-container\",\n    children: assignments.map(assignment => /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      sm: 6,\n      md: 4,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"grid-card\",\n        children: [/*#__PURE__*/_jsxDEV(CardContent, {\n          className: \"grid-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            className: \"user-name\",\n            children: assignment.user.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: assignment.user.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: [\"Department: \", assignment.user.department]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            className: \"associate-text\",\n            children: [\"Associate: \", assignment.associateApprover.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: assignment.associateApprover.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this), assignment.backupApprover && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              className: \"backup-text\",\n              children: [\"Backup: \", assignment.backupApprover.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: assignment.backupApprover.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"assigned-date\",\n            children: [\"Assigned: \", assignment.assignedDate]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handleRemoveAssignment(assignment.id),\n            color: \"error\",\n            size: \"small\",\n            startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 28\n            }, this),\n            className: \"grid-delete-btn\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this)\n    }, assignment.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n  const renderCardView = () => /*#__PURE__*/_jsxDEV(Box, {\n    className: \"card-container\",\n    children: assignments.map(assignment => /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 1,\n      className: \"card-item\",\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          className: \"card-content-flex\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"card-content-main\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              className: \"card-user-name\",\n              children: assignment.user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: [assignment.user.email, \" \\u2022 \", assignment.user.department]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"card-approvers-box\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: 600,\n                  children: \"Associate Approver\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: assignment.associateApprover.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: assignment.associateApprover.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: 600,\n                  children: \"Backup Approver\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this), assignment.backupApprover ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: assignment.backupApprover.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: assignment.backupApprover.role\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  fontStyle: \"italic\",\n                  children: \"No backup assigned\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: 600,\n                  children: \"Assigned Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: assignment.assignedDate\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handleRemoveAssignment(assignment.id),\n            color: \"error\",\n            size: \"small\",\n            startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 28\n            }, this),\n            className: \"card-delete-btn\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this)\n    }, assignment.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 5\n  }, this);\n  const renderListView = () => /*#__PURE__*/_jsxDEV(List, {\n    className: \"list-container\",\n    children: assignments.map((assignment, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(ListItem, {\n        children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            className: \"list-user-name\",\n            children: assignment.user.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 17\n          }, this),\n          secondary: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"list-user-info\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [assignment.user.email, \" \\u2022 \", assignment.user.department]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Associate: \", assignment.associateApprover.name, \" (\", assignment.associateApprover.role, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 19\n            }, this), assignment.backupApprover && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Backup: \", assignment.backupApprover.name, \" (\", assignment.backupApprover.role, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [\"Assigned: \", assignment.assignedDate]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handleRemoveAssignment(assignment.id),\n            color: \"error\",\n            size: \"small\",\n            startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 28\n            }, this),\n            className: \"list-delete-btn\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this), index < assignments.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 46\n      }, this)]\n    }, assignment.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 5\n  }, this);\n  const renderTableView = () => /*#__PURE__*/_jsxDEV(TableContainer, {\n    className: \"table-container\",\n    children: /*#__PURE__*/_jsxDEV(Table, {\n      children: [/*#__PURE__*/_jsxDEV(TableHead, {\n        children: /*#__PURE__*/_jsxDEV(TableRow, {\n          className: \"table-header-row\",\n          children: [/*#__PURE__*/_jsxDEV(TableCell, {\n            className: \"table-header-cell\",\n            children: \"User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            className: \"table-header-cell\",\n            children: \"Department\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            className: \"table-header-cell\",\n            children: \"Associate Approver\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            className: \"table-header-cell\",\n            children: \"Backup Approver\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            className: \"table-header-cell\",\n            children: \"Assigned Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            className: \"table-header-cell\",\n            children: \"Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n        children: assignments.map(assignment => /*#__PURE__*/_jsxDEV(TableRow, {\n          hover: true,\n          children: [/*#__PURE__*/_jsxDEV(TableCell, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: 600,\n                children: assignment.user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: assignment.user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: assignment.user.department\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: 600,\n                children: assignment.associateApprover.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: assignment.associateApprover.role\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: assignment.backupApprover ? /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: 600,\n                children: assignment.backupApprover.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: assignment.backupApprover.role\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              fontStyle: \"italic\",\n              children: \"No backup assigned\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: assignment.assignedDate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => handleRemoveAssignment(assignment.id),\n              color: \"error\",\n              size: \"small\",\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 30\n              }, this),\n              className: \"table-delete-btn\",\n              children: \"Delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)]\n        }, assignment.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 281,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    className: \"approver-container\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: \"header-box\",\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h3\",\n        component: \"h1\",\n        gutterBottom: true,\n        className: \"main-title\",\n        children: \"Associate Approver and Backup Approver To User\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        className: \"subtitle\",\n        children: \"Assign associate and backup approvers to users for workflow management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), notification.message && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: notification.type,\n      onClose: () => setNotification({\n        type: '',\n        message: ''\n      }),\n      sx: {\n        mb: 3\n      },\n      children: notification.message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 3,\n          className: \"form-paper\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            className: \"form-title\",\n            children: \"Assign Approvers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            component: \"form\",\n            className: \"form-box\",\n            children: [/*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: availableUsers,\n              getOptionLabel: option => option.name,\n              value: selectedUser,\n              onChange: (_, newValue) => setSelectedUser(newValue),\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                label: \"Select User\",\n                margin: \"normal\",\n                fullWidth: true,\n                sx: {\n                  '& .MuiInputLabel-root': {\n                    fontFamily: '\"Inter\", sans-serif'\n                  },\n                  '& .MuiInputBase-input': {\n                    fontFamily: '\"Inter\", sans-serif'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 19\n              }, this),\n              className: \"form-autocomplete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: availableApprovers,\n              getOptionLabel: option => `${option.name} - ${option.role}`,\n              value: associateApprover,\n              onChange: (_, newValue) => setAssociateApprover(newValue),\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                label: \"Associate Approver *\",\n                margin: \"normal\",\n                fullWidth: true,\n                required: true,\n                sx: {\n                  '& .MuiInputLabel-root': {\n                    fontFamily: '\"Inter\", sans-serif'\n                  },\n                  '& .MuiInputBase-input': {\n                    fontFamily: '\"Inter\", sans-serif'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this),\n              className: \"form-autocomplete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: availableApprovers.filter(approver => approver.id !== (associateApprover === null || associateApprover === void 0 ? void 0 : associateApprover.id)),\n              getOptionLabel: option => `${option.name} - ${option.role}`,\n              value: backupApprover,\n              onChange: (_, newValue) => setBackupApprover(newValue),\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                label: \"Backup Approver (Optional)\",\n                margin: \"normal\",\n                fullWidth: true,\n                sx: {\n                  '& .MuiInputLabel-root': {\n                    fontFamily: '\"Inter\", sans-serif'\n                  },\n                  '& .MuiInputBase-input': {\n                    fontFamily: '\"Inter\", sans-serif'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this),\n              className: \"form-autocomplete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              size: \"large\",\n              onClick: handleAssignApprovers,\n              startIcon: /*#__PURE__*/_jsxDEV(PersonAddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 28\n              }, this),\n              className: \"assign-button\",\n              children: \"Assign Approvers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 3,\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              sx: {\n                borderBottom: 2,\n                borderColor: '#2EC0CB',\n                pb: 1,\n                fontFamily: '\"Inter\", sans-serif',\n                fontWeight: 600,\n                color: '#2EC0CB'\n              },\n              children: [\"Current Assignments (\", assignments.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToggleButtonGroup, {\n              value: viewMode,\n              exclusive: true,\n              onChange: handleViewChange,\n              size: \"small\",\n              sx: {\n                '& .MuiToggleButton-root': {\n                  fontFamily: '\"Inter\", sans-serif',\n                  border: '1px solid #2EC0CB',\n                  color: '#2EC0CB',\n                  display: 'flex',\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  gap: 1,\n                  textTransform: 'none',\n                  px: 2,\n                  py: 1,\n                  '&.Mui-selected': {\n                    backgroundColor: '#2EC0CB',\n                    color: 'white',\n                    '&:hover': {\n                      backgroundColor: '#208B94'\n                    }\n                  },\n                  '&:hover': {\n                    backgroundColor: 'rgba(46, 192, 203, 0.1)'\n                  }\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(ToggleButton, {\n                value: \"table\",\n                title: \"Table View\",\n                children: [/*#__PURE__*/_jsxDEV(TableViewIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    ml: 0.5\n                  },\n                  children: \"Table\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n                value: \"grid\",\n                title: \"Grid View\",\n                children: [/*#__PURE__*/_jsxDEV(GridViewIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    ml: 0.5\n                  },\n                  children: \"Grid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n                value: \"card\",\n                title: \"Card View\",\n                children: [/*#__PURE__*/_jsxDEV(CardViewIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    ml: 0.5\n                  },\n                  children: \"Card\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n                value: \"list\",\n                title: \"List View\",\n                children: [/*#__PURE__*/_jsxDEV(ListViewIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    ml: 0.5\n                  },\n                  children: \"List\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), assignments.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"center\",\n            py: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              children: \"No approver assignments yet. Create your first assignment using the form.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [viewMode === 'table' && renderTableView(), viewMode === 'grid' && renderGridView(), viewMode === 'card' && renderCardView(), viewMode === 'list' && renderListView()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 354,\n    columnNumber: 5\n  }, this);\n};\n_s(ApproverManagement, \"MMTYmuG0zfwW+cXrZm2Rxwmv1E8=\");\n_c = ApproverManagement;\nexport default ApproverManagement;\nvar _c;\n$RefreshReg$(_c, \"ApproverManagement\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Paper", "Typography", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Grid", "Box", "Chip", "IconButton", "<PERSON><PERSON>", "Autocomplete", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "ToggleButton", "ToggleButtonGroup", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Divider", "PersonAdd", "PersonAddIcon", "Delete", "DeleteIcon", "ViewModule", "GridViewIcon", "ViewList", "ListViewIcon", "ViewStream", "CardViewIcon", "Table<PERSON>hart", "TableViewIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ApproverManagement", "_s", "users", "setUsers", "selected<PERSON>ser", "setSelectedUser", "associateApprover", "setAssociateApprover", "backupApprover", "setBackupApprover", "assignments", "setAssignments", "notification", "setNotification", "type", "message", "viewMode", "setViewMode", "availableUsers", "id", "name", "email", "department", "availableApprovers", "role", "handleAssignApprovers", "newAssignment", "Date", "now", "user", "assignedDate", "toLocaleDateString", "handleRemoveAssignment", "filter", "assignment", "handleViewChange", "event", "newView", "renderGridView", "container", "spacing", "className", "children", "map", "item", "xs", "sm", "md", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "onClick", "size", "startIcon", "renderCardView", "elevation", "fontWeight", "fontStyle", "renderListView", "index", "primary", "secondary", "length", "renderTableView", "hover", "max<PERSON><PERSON><PERSON>", "component", "severity", "onClose", "sx", "mb", "options", "getOptionLabel", "option", "value", "onChange", "_", "newValue", "renderInput", "params", "label", "margin", "fullWidth", "fontFamily", "required", "approver", "p", "display", "justifyContent", "alignItems", "borderBottom", "borderColor", "pb", "exclusive", "border", "flexDirection", "gap", "textTransform", "px", "py", "backgroundColor", "title", "fontSize", "ml", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/React/TLT_Links/linkapp/src/Components/ApproverManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n  Container,\r\n  Paper,\r\n  Typography,\r\n  TextField,\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n  Button,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Grid,\r\n  Box,\r\n  Chip,\r\n  IconButton,\r\n  Alert,\r\n  Autocomplete,\r\n  Card,\r\n  CardContent,\r\n  CardActions,\r\n  ToggleButton,\r\n  ToggleButtonGroup,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemSecondaryAction,\r\n  Divider\r\n} from '@mui/material';\r\nimport {\r\n  PersonAdd as PersonAddIcon,\r\n  Delete as DeleteIcon,\r\n  ViewModule as GridViewIcon,\r\n  ViewList as ListViewIcon,\r\n  ViewStream as CardViewIcon,\r\n  TableChart as TableViewIcon\r\n} from '@mui/icons-material';\r\nimport './ApproverManagement.css';\r\n\r\nconst ApproverManagement = () => {\r\n  const [users, setUsers] = useState([]);\r\n  const [selectedUser, setSelectedUser] = useState(null);\r\n  const [associateApprover, setAssociateApprover] = useState(null);\r\n  const [backupApprover, setBackupApprover] = useState(null);\r\n  const [assignments, setAssignments] = useState([]);\r\n  const [notification, setNotification] = useState({ type: '', message: '' });\r\n  const [viewMode, setViewMode] = useState('table'); // 'table', 'grid', 'card', 'list'\r\n\r\n  // Sample users data (in real app, this would come from an API)\r\n  const availableUsers = [\r\n    { id: 1, name: 'John Doe', email: '<EMAIL>', department: 'Engineering' },\r\n    { id: 2, name: 'Jane Smith', email: '<EMAIL>', department: 'Marketing' },\r\n    { id: 3, name: 'Mike Johnson', email: '<EMAIL>', department: 'Sales' },\r\n    { id: 4, name: 'Sarah Wilson', email: '<EMAIL>', department: 'HR' },\r\n    { id: 5, name: 'David Brown', email: '<EMAIL>', department: 'Finance' },\r\n    { id: 6, name: 'Lisa Davis', email: '<EMAIL>', department: 'Operations' },\r\n  ];\r\n\r\n  const availableApprovers = [\r\n    { id: 101, name: 'Manager A', email: '<EMAIL>', role: 'Team Lead' },\r\n    { id: 102, name: 'Manager B', email: '<EMAIL>', role: 'Department Head' },\r\n    { id: 103, name: 'Manager C', email: '<EMAIL>', role: 'Senior Manager' },\r\n    { id: 104, name: 'Manager D', email: '<EMAIL>', role: 'Director' },\r\n  ];\r\n\r\n  const handleAssignApprovers = () => {\r\n    if (!selectedUser || !associateApprover) {\r\n      setNotification({ type: 'error', message: 'Please select a user and at least an associate approver' });\r\n      return;\r\n    }\r\n\r\n    if (associateApprover?.id === backupApprover?.id) {\r\n      setNotification({ type: 'error', message: 'Associate and backup approvers must be different' });\r\n      return;\r\n    }\r\n\r\n    const newAssignment = {\r\n      id: Date.now(),\r\n      user: selectedUser,\r\n      associateApprover: associateApprover,\r\n      backupApprover: backupApprover,\r\n      assignedDate: new Date().toLocaleDateString()\r\n    };\r\n\r\n    setAssignments([...assignments, newAssignment]);\r\n    \r\n    // Reset form\r\n    setSelectedUser(null);\r\n    setAssociateApprover(null);\r\n    setBackupApprover(null);\r\n    \r\n    setNotification({ type: 'success', message: 'Approvers assigned successfully!' });\r\n  };\r\n\r\n  const handleRemoveAssignment = (id) => {\r\n    setAssignments(assignments.filter(assignment => assignment.id !== id));\r\n  };\r\n\r\n  const handleViewChange = (event, newView) => {\r\n    if (newView !== null) {\r\n      setViewMode(newView);\r\n    }\r\n  };\r\n\r\n  const renderGridView = () => (\r\n    <Grid container spacing={3} className=\"grid-container\">\r\n      {assignments.map(assignment => (\r\n        <Grid item xs={12} sm={6} md={4} key={assignment.id}>\r\n          <Card className=\"grid-card\">\r\n            <CardContent className=\"grid-card-content\">\r\n              <Typography variant=\"h6\" gutterBottom className=\"user-name\">\r\n                {assignment.user.name}\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\r\n                {assignment.user.email}\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\r\n                Department: {assignment.user.department}\r\n              </Typography>\r\n              <Typography variant=\"body2\" className=\"associate-text\">\r\n                Associate: {assignment.associateApprover.name}\r\n              </Typography>\r\n              <Typography variant=\"caption\" color=\"text.secondary\">\r\n                {assignment.associateApprover.role}\r\n              </Typography>\r\n              {assignment.backupApprover && (\r\n                <>\r\n                  <Typography variant=\"body2\" className=\"backup-text\">\r\n                    Backup: {assignment.backupApprover.name}\r\n                  </Typography>\r\n                  <Typography variant=\"caption\" color=\"text.secondary\">\r\n                    {assignment.backupApprover.role}\r\n                  </Typography>\r\n                </>\r\n              )}\r\n              <Typography variant=\"caption\" className=\"assigned-date\">\r\n                Assigned: {assignment.assignedDate}\r\n              </Typography>\r\n            </CardContent>\r\n            <CardActions>\r\n              <Button\r\n                onClick={() => handleRemoveAssignment(assignment.id)}\r\n                color=\"error\"\r\n                size=\"small\"\r\n                startIcon={<DeleteIcon />}\r\n                className=\"grid-delete-btn\"\r\n              >\r\n                Delete\r\n              </Button>\r\n            </CardActions>\r\n          </Card>\r\n        </Grid>\r\n      ))}\r\n    </Grid>\r\n  );\r\n\r\n  const renderCardView = () => (\r\n    <Box className=\"card-container\">\r\n      {assignments.map((assignment) => (\r\n        <Card key={assignment.id} elevation={1} className=\"card-item\">\r\n          <CardContent>\r\n            <Box className=\"card-content-flex\">\r\n              <Box className=\"card-content-main\">\r\n                <Typography variant=\"h6\" className=\"card-user-name\">\r\n                  {assignment.user.name}\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\r\n                  {assignment.user.email} • {assignment.user.department}\r\n                </Typography>\r\n                <Box className=\"card-approvers-box\">\r\n                  <Box>\r\n                    <Typography variant=\"body2\" fontWeight={600}>\r\n                      Associate Approver\r\n                    </Typography>\r\n                    <Typography variant=\"body2\">\r\n                      {assignment.associateApprover.name}\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" color=\"text.secondary\">\r\n                      {assignment.associateApprover.role}\r\n                    </Typography>\r\n                  </Box>\r\n                  <Box>\r\n                    <Typography variant=\"body2\" fontWeight={600}>\r\n                      Backup Approver\r\n                    </Typography>\r\n                    {assignment.backupApprover ? (\r\n                      <>\r\n                        <Typography variant=\"body2\">\r\n                          {assignment.backupApprover.name}\r\n                        </Typography>\r\n                        <Typography variant=\"caption\" color=\"text.secondary\">\r\n                          {assignment.backupApprover.role}\r\n                        </Typography>\r\n                      </>\r\n                    ) : (\r\n                      <Typography variant=\"body2\" color=\"text.secondary\" fontStyle=\"italic\">\r\n                        No backup assigned\r\n                      </Typography>\r\n                    )}\r\n                  </Box>\r\n                  <Box>\r\n                    <Typography variant=\"body2\" fontWeight={600}>\r\n                      Assigned Date\r\n                    </Typography>\r\n                    <Typography variant=\"body2\">\r\n                      {assignment.assignedDate}\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n              <Button\r\n                onClick={() => handleRemoveAssignment(assignment.id)}\r\n                color=\"error\"\r\n                size=\"small\"\r\n                startIcon={<DeleteIcon />}\r\n                className=\"card-delete-btn\"\r\n              >\r\n                Delete\r\n              </Button>\r\n            </Box>\r\n          </CardContent>\r\n        </Card>\r\n      ))}\r\n    </Box>\r\n  );\r\n\r\n  const renderListView = () => (\r\n    <List className=\"list-container\">\r\n      {assignments.map((assignment, index) => (\r\n        <React.Fragment key={assignment.id}>\r\n          <ListItem>\r\n            <ListItemText\r\n              primary={\r\n                <Typography variant=\"h6\" className=\"list-user-name\">\r\n                  {assignment.user.name}\r\n                </Typography>\r\n              }\r\n              secondary={\r\n                <Box className=\"list-user-info\">\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    {assignment.user.email} • {assignment.user.department}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\">\r\n                    Associate: {assignment.associateApprover.name} ({assignment.associateApprover.role})\r\n                  </Typography>\r\n                  {assignment.backupApprover && (\r\n                    <Typography variant=\"body2\">\r\n                      Backup: {assignment.backupApprover.name} ({assignment.backupApprover.role})\r\n                    </Typography>\r\n                  )}\r\n                  <Typography variant=\"caption\" color=\"text.secondary\">\r\n                    Assigned: {assignment.assignedDate}\r\n                  </Typography>\r\n                </Box>\r\n              }\r\n            />\r\n            <ListItemSecondaryAction>\r\n              <Button\r\n                onClick={() => handleRemoveAssignment(assignment.id)}\r\n                color=\"error\"\r\n                size=\"small\"\r\n                startIcon={<DeleteIcon />}\r\n                className=\"list-delete-btn\"\r\n              >\r\n                Delete\r\n              </Button>\r\n            </ListItemSecondaryAction>\r\n          </ListItem>\r\n          {index < assignments.length - 1 && <Divider />}\r\n        </React.Fragment>\r\n      ))}\r\n    </List>\r\n  );\r\n\r\n  const renderTableView = () => (\r\n    <TableContainer className=\"table-container\">\r\n      <Table>\r\n        <TableHead>\r\n          <TableRow className=\"table-header-row\">\r\n            <TableCell className=\"table-header-cell\">User</TableCell>\r\n            <TableCell className=\"table-header-cell\">Department</TableCell>\r\n            <TableCell className=\"table-header-cell\">Associate Approver</TableCell>\r\n            <TableCell className=\"table-header-cell\">Backup Approver</TableCell>\r\n            <TableCell className=\"table-header-cell\">Assigned Date</TableCell>\r\n            <TableCell className=\"table-header-cell\">Actions</TableCell>\r\n          </TableRow>\r\n        </TableHead>\r\n        <TableBody>\r\n          {assignments.map(assignment => (\r\n            <TableRow key={assignment.id} hover>\r\n              <TableCell>\r\n                <Box>\r\n                  <Typography variant=\"body2\" fontWeight={600}>\r\n                    {assignment.user.name}\r\n                  </Typography>\r\n                  <Typography variant=\"caption\" color=\"text.secondary\">\r\n                    {assignment.user.email}\r\n                  </Typography>\r\n                </Box>\r\n              </TableCell>\r\n              <TableCell>{assignment.user.department}</TableCell>\r\n              <TableCell>\r\n                <Box>\r\n                  <Typography variant=\"body2\" fontWeight={600}>\r\n                    {assignment.associateApprover.name}\r\n                  </Typography>\r\n                  <Typography variant=\"caption\" color=\"text.secondary\">\r\n                    {assignment.associateApprover.role}\r\n                  </Typography>\r\n                </Box>\r\n              </TableCell>\r\n              <TableCell>\r\n                {assignment.backupApprover ? (\r\n                  <Box>\r\n                    <Typography variant=\"body2\" fontWeight={600}>\r\n                      {assignment.backupApprover.name}\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" color=\"text.secondary\">\r\n                      {assignment.backupApprover.role}\r\n                    </Typography>\r\n                  </Box>\r\n                ) : (\r\n                  <Typography variant=\"body2\" color=\"text.secondary\" fontStyle=\"italic\">\r\n                    No backup assigned\r\n                  </Typography>\r\n                )}\r\n              </TableCell>\r\n              <TableCell>{assignment.assignedDate}</TableCell>\r\n              <TableCell>\r\n                <Button\r\n                  onClick={() => handleRemoveAssignment(assignment.id)}\r\n                  color=\"error\"\r\n                  size=\"small\"\r\n                  variant=\"contained\"\r\n                  startIcon={<DeleteIcon />}\r\n                  className=\"table-delete-btn\"\r\n                >\r\n                  Delete\r\n                </Button>\r\n              </TableCell>\r\n            </TableRow>\r\n          ))}\r\n        </TableBody>\r\n      </Table>\r\n    </TableContainer>\r\n  );\r\n\r\n  return (\r\n    <Container maxWidth=\"xl\" className=\"approver-container\">\r\n      {/* Header */}\r\n      <Box className=\"header-box\">\r\n        <Typography\r\n          variant=\"h3\"\r\n          component=\"h1\"\r\n          gutterBottom\r\n          className=\"main-title\"\r\n        >\r\n          Associate Approver and Backup Approver To User\r\n        </Typography>\r\n        <Typography\r\n          variant=\"h6\"\r\n          color=\"text.secondary\"\r\n          className=\"subtitle\"\r\n        >\r\n          Assign associate and backup approvers to users for workflow management\r\n        </Typography>\r\n      </Box>\r\n\r\n      {/* Notification */}\r\n      {notification.message && (\r\n        <Alert\r\n          severity={notification.type}\r\n          onClose={() => setNotification({ type: '', message: '' })}\r\n          sx={{ mb: 3 }}\r\n        >\r\n          {notification.message}\r\n        </Alert>\r\n      )}\r\n\r\n      <Grid container spacing={4}>\r\n        {/* Assignment Form */}\r\n        <Grid item xs={12} md={4}>\r\n          <Paper elevation={3} className=\"form-paper\">\r\n            <Typography\r\n              variant=\"h5\"\r\n              gutterBottom\r\n              className=\"form-title\"\r\n            >\r\n              Assign Approvers\r\n            </Typography>\r\n\r\n            <Box component=\"form\" className=\"form-box\">\r\n              <Autocomplete\r\n                options={availableUsers}\r\n                getOptionLabel={(option) => option.name}\r\n                value={selectedUser}\r\n                onChange={(_, newValue) => setSelectedUser(newValue)}\r\n                renderInput={(params) => (\r\n                  <TextField\r\n                    {...params}\r\n                    label=\"Select User\"\r\n                    margin=\"normal\"\r\n                    fullWidth\r\n                    sx={{\r\n                      '& .MuiInputLabel-root': {\r\n                        fontFamily: '\"Inter\", sans-serif',\r\n                      },\r\n                      '& .MuiInputBase-input': {\r\n                        fontFamily: '\"Inter\", sans-serif',\r\n                      },\r\n                    }}\r\n                  />\r\n                )}\r\n                className=\"form-autocomplete\"\r\n              />\r\n\r\n              <Autocomplete\r\n                options={availableApprovers}\r\n                getOptionLabel={(option) => `${option.name} - ${option.role}`}\r\n                value={associateApprover}\r\n                onChange={(_, newValue) => setAssociateApprover(newValue)}\r\n                renderInput={(params) => (\r\n                  <TextField\r\n                    {...params}\r\n                    label=\"Associate Approver *\"\r\n                    margin=\"normal\"\r\n                    fullWidth\r\n                    required\r\n                    sx={{\r\n                      '& .MuiInputLabel-root': {\r\n                        fontFamily: '\"Inter\", sans-serif',\r\n                      },\r\n                      '& .MuiInputBase-input': {\r\n                        fontFamily: '\"Inter\", sans-serif',\r\n                      },\r\n                    }}\r\n                  />\r\n                )}\r\n                className=\"form-autocomplete\"\r\n              />\r\n\r\n              <Autocomplete\r\n                options={availableApprovers.filter(approver => approver.id !== associateApprover?.id)}\r\n                getOptionLabel={(option) => `${option.name} - ${option.role}`}\r\n                value={backupApprover}\r\n                onChange={(_, newValue) => setBackupApprover(newValue)}\r\n                renderInput={(params) => (\r\n                  <TextField\r\n                    {...params}\r\n                    label=\"Backup Approver (Optional)\"\r\n                    margin=\"normal\"\r\n                    fullWidth\r\n                    sx={{\r\n                      '& .MuiInputLabel-root': {\r\n                        fontFamily: '\"Inter\", sans-serif',\r\n                      },\r\n                      '& .MuiInputBase-input': {\r\n                        fontFamily: '\"Inter\", sans-serif',\r\n                      },\r\n                    }}\r\n                  />\r\n                )}\r\n                className=\"form-autocomplete\"\r\n              />\r\n\r\n              <Button\r\n                fullWidth\r\n                variant=\"contained\"\r\n                size=\"large\"\r\n                onClick={handleAssignApprovers}\r\n                startIcon={<PersonAddIcon />}\r\n                className=\"assign-button\"\r\n              >\r\n                Assign Approvers\r\n              </Button>\r\n            </Box>\r\n          </Paper>\r\n        </Grid>\r\n\r\n        {/* Current Assignments */}\r\n        <Grid item xs={12} md={8}>\r\n          <Paper elevation={3} sx={{ p: 3 }}>\r\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\r\n              <Typography \r\n                variant=\"h5\" \r\n                sx={{ \r\n                  borderBottom: 2, \r\n                  borderColor: '#2EC0CB', \r\n                  pb: 1,\r\n                  fontFamily: '\"Inter\", sans-serif',\r\n                  fontWeight: 600,\r\n                  color: '#2EC0CB'\r\n                }}\r\n              >\r\n                Current Assignments ({assignments.length})\r\n              </Typography>\r\n              \r\n              <ToggleButtonGroup\r\n                value={viewMode}\r\n                exclusive\r\n                onChange={handleViewChange}\r\n                size=\"small\"\r\n                sx={{\r\n                  '& .MuiToggleButton-root': {\r\n                    fontFamily: '\"Inter\", sans-serif',\r\n                    border: '1px solid #2EC0CB',\r\n                    color: '#2EC0CB',\r\n                    display: 'flex',\r\n                    flexDirection: 'row',\r\n                    alignItems: 'center',\r\n                    gap: 1,\r\n                    textTransform: 'none',\r\n                    px: 2,\r\n                    py: 1,\r\n                    '&.Mui-selected': {\r\n                      backgroundColor: '#2EC0CB',\r\n                      color: 'white',\r\n                      '&:hover': {\r\n                        backgroundColor: '#208B94',\r\n                      },\r\n                    },\r\n                    '&:hover': {\r\n                      backgroundColor: 'rgba(46, 192, 203, 0.1)',\r\n                    },\r\n                  },\r\n                }}\r\n              >\r\n                <ToggleButton value=\"table\" title=\"Table View\">\r\n                  <TableViewIcon fontSize=\"small\" />\r\n                  <Typography variant=\"caption\" sx={{ ml: 0.5 }}>Table</Typography>\r\n                </ToggleButton>\r\n                <ToggleButton value=\"grid\" title=\"Grid View\">\r\n                  <GridViewIcon fontSize=\"small\" />\r\n                  <Typography variant=\"caption\" sx={{ ml: 0.5 }}>Grid</Typography>\r\n                </ToggleButton>\r\n                <ToggleButton value=\"card\" title=\"Card View\">\r\n                  <CardViewIcon fontSize=\"small\" />\r\n                  <Typography variant=\"caption\" sx={{ ml: 0.5 }}>Card</Typography>\r\n                </ToggleButton>\r\n                <ToggleButton value=\"list\" title=\"List View\">\r\n                  <ListViewIcon fontSize=\"small\" />\r\n                  <Typography variant=\"caption\" sx={{ ml: 0.5 }}>List</Typography>\r\n                </ToggleButton>\r\n              </ToggleButtonGroup>\r\n            </Box>\r\n            \r\n            {assignments.length === 0 ? (\r\n              <Box textAlign=\"center\" py={6}>\r\n                <Typography variant=\"h6\" color=\"text.secondary\">\r\n                  No approver assignments yet. Create your first assignment using the form.\r\n                </Typography>\r\n              </Box>\r\n            ) : (\r\n              <Box>\r\n                {viewMode === 'table' && renderTableView()}\r\n                {viewMode === 'grid' && renderGridView()}\r\n                {viewMode === 'card' && renderCardView()}\r\n                {viewMode === 'list' && renderListView()}\r\n              </Box>\r\n            )}\r\n          </Paper>\r\n        </Grid>\r\n      </Grid>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default ApproverManagement;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,YAAY,EACZC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,iBAAiB,EACjBC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,OAAO,QACF,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,YAAY,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,YAAY,EAC1BC,UAAU,IAAIC,aAAa,QACtB,qBAAqB;AAC5B,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0D,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC;IAAE8D,IAAI,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG,CAAC,CAAC;EAC3E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAMkE,cAAc,GAAG,CACrB;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,sBAAsB;IAAEC,UAAU,EAAE;EAAc,CAAC,EACrF;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,wBAAwB;IAAEC,UAAU,EAAE;EAAY,CAAC,EACvF;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,0BAA0B;IAAEC,UAAU,EAAE;EAAQ,CAAC,EACvF;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,0BAA0B;IAAEC,UAAU,EAAE;EAAK,CAAC,EACpF;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,yBAAyB;IAAEC,UAAU,EAAE;EAAU,CAAC,EACvF;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,wBAAwB;IAAEC,UAAU,EAAE;EAAa,CAAC,CACzF;EAED,MAAMC,kBAAkB,GAAG,CACzB;IAAEJ,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,uBAAuB;IAAEG,IAAI,EAAE;EAAY,CAAC,EACjF;IAAEL,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,uBAAuB;IAAEG,IAAI,EAAE;EAAkB,CAAC,EACvF;IAAEL,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,uBAAuB;IAAEG,IAAI,EAAE;EAAiB,CAAC,EACtF;IAAEL,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,uBAAuB;IAAEG,IAAI,EAAE;EAAW,CAAC,CACjF;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACrB,YAAY,IAAI,CAACE,iBAAiB,EAAE;MACvCO,eAAe,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,OAAO,EAAE;MAA0D,CAAC,CAAC;MACtG;IACF;IAEA,IAAI,CAAAT,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEa,EAAE,OAAKX,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEW,EAAE,GAAE;MAChDN,eAAe,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAmD,CAAC,CAAC;MAC/F;IACF;IAEA,MAAMW,aAAa,GAAG;MACpBP,EAAE,EAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,IAAI,EAAEzB,YAAY;MAClBE,iBAAiB,EAAEA,iBAAiB;MACpCE,cAAc,EAAEA,cAAc;MAC9BsB,YAAY,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,kBAAkB,CAAC;IAC9C,CAAC;IAEDpB,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEgB,aAAa,CAAC,CAAC;;IAE/C;IACArB,eAAe,CAAC,IAAI,CAAC;IACrBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,iBAAiB,CAAC,IAAI,CAAC;IAEvBI,eAAe,CAAC;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAmC,CAAC,CAAC;EACnF,CAAC;EAED,MAAMiB,sBAAsB,GAAIb,EAAE,IAAK;IACrCR,cAAc,CAACD,WAAW,CAACuB,MAAM,CAACC,UAAU,IAAIA,UAAU,CAACf,EAAE,KAAKA,EAAE,CAAC,CAAC;EACxE,CAAC;EAED,MAAMgB,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC3C,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpBpB,WAAW,CAACoB,OAAO,CAAC;IACtB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,kBACrBzC,OAAA,CAAC7B,IAAI;IAACuE,SAAS;IAACC,OAAO,EAAE,CAAE;IAACC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,EACnDhC,WAAW,CAACiC,GAAG,CAACT,UAAU,iBACzBrC,OAAA,CAAC7B,IAAI;MAAC4E,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAAAL,QAAA,eAC9B7C,OAAA,CAACvB,IAAI;QAACmE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACzB7C,OAAA,CAACtB,WAAW;UAACkE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACxC7C,OAAA,CAAC1C,UAAU;YAAC6F,OAAO,EAAC,IAAI;YAACC,YAAY;YAACR,SAAS,EAAC,WAAW;YAAAC,QAAA,EACxDR,UAAU,CAACL,IAAI,CAACT;UAAI;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACbxD,OAAA,CAAC1C,UAAU;YAAC6F,OAAO,EAAC,OAAO;YAACM,KAAK,EAAC,gBAAgB;YAACL,YAAY;YAAAP,QAAA,EAC5DR,UAAU,CAACL,IAAI,CAACR;UAAK;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbxD,OAAA,CAAC1C,UAAU;YAAC6F,OAAO,EAAC,OAAO;YAACM,KAAK,EAAC,gBAAgB;YAACL,YAAY;YAAAP,QAAA,GAAC,cAClD,EAACR,UAAU,CAACL,IAAI,CAACP,UAAU;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACbxD,OAAA,CAAC1C,UAAU;YAAC6F,OAAO,EAAC,OAAO;YAACP,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAAC,aAC1C,EAACR,UAAU,CAAC5B,iBAAiB,CAACc,IAAI;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACbxD,OAAA,CAAC1C,UAAU;YAAC6F,OAAO,EAAC,SAAS;YAACM,KAAK,EAAC,gBAAgB;YAAAZ,QAAA,EACjDR,UAAU,CAAC5B,iBAAiB,CAACkB;UAAI;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,EACZnB,UAAU,CAAC1B,cAAc,iBACxBX,OAAA,CAAAE,SAAA;YAAA2C,QAAA,gBACE7C,OAAA,CAAC1C,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAACP,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAC,UAC1C,EAACR,UAAU,CAAC1B,cAAc,CAACY,IAAI;YAAA;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbxD,OAAA,CAAC1C,UAAU;cAAC6F,OAAO,EAAC,SAAS;cAACM,KAAK,EAAC,gBAAgB;cAAAZ,QAAA,EACjDR,UAAU,CAAC1B,cAAc,CAACgB;YAAI;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA,eACb,CACH,eACDxD,OAAA,CAAC1C,UAAU;YAAC6F,OAAO,EAAC,SAAS;YAACP,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,YAC5C,EAACR,UAAU,CAACJ,YAAY;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACdxD,OAAA,CAACrB,WAAW;UAAAkE,QAAA,eACV7C,OAAA,CAACpC,MAAM;YACL8F,OAAO,EAAEA,CAAA,KAAMvB,sBAAsB,CAACE,UAAU,CAACf,EAAE,CAAE;YACrDmC,KAAK,EAAC,OAAO;YACbE,IAAI,EAAC,OAAO;YACZC,SAAS,eAAE5D,OAAA,CAACV,UAAU;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BZ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC5B;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC,GA3C6BnB,UAAU,CAACf,EAAE;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA4C7C,CACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACP;EAED,MAAMK,cAAc,GAAGA,CAAA,kBACrB7D,OAAA,CAAC5B,GAAG;IAACwE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,EAC5BhC,WAAW,CAACiC,GAAG,CAAET,UAAU,iBAC1BrC,OAAA,CAACvB,IAAI;MAAqBqF,SAAS,EAAE,CAAE;MAAClB,SAAS,EAAC,WAAW;MAAAC,QAAA,eAC3D7C,OAAA,CAACtB,WAAW;QAAAmE,QAAA,eACV7C,OAAA,CAAC5B,GAAG;UAACwE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7C,OAAA,CAAC5B,GAAG;YAACwE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC7C,OAAA,CAAC1C,UAAU;cAAC6F,OAAO,EAAC,IAAI;cAACP,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAChDR,UAAU,CAACL,IAAI,CAACT;YAAI;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACbxD,OAAA,CAAC1C,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAACM,KAAK,EAAC,gBAAgB;cAACL,YAAY;cAAAP,QAAA,GAC5DR,UAAU,CAACL,IAAI,CAACR,KAAK,EAAC,UAAG,EAACa,UAAU,CAACL,IAAI,CAACP,UAAU;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACbxD,OAAA,CAAC5B,GAAG;cAACwE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjC7C,OAAA,CAAC5B,GAAG;gBAAAyE,QAAA,gBACF7C,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,OAAO;kBAACY,UAAU,EAAE,GAAI;kBAAAlB,QAAA,EAAC;gBAE7C;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbxD,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,OAAO;kBAAAN,QAAA,EACxBR,UAAU,CAAC5B,iBAAiB,CAACc;gBAAI;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACbxD,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,SAAS;kBAACM,KAAK,EAAC,gBAAgB;kBAAAZ,QAAA,EACjDR,UAAU,CAAC5B,iBAAiB,CAACkB;gBAAI;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNxD,OAAA,CAAC5B,GAAG;gBAAAyE,QAAA,gBACF7C,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,OAAO;kBAACY,UAAU,EAAE,GAAI;kBAAAlB,QAAA,EAAC;gBAE7C;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EACZnB,UAAU,CAAC1B,cAAc,gBACxBX,OAAA,CAAAE,SAAA;kBAAA2C,QAAA,gBACE7C,OAAA,CAAC1C,UAAU;oBAAC6F,OAAO,EAAC,OAAO;oBAAAN,QAAA,EACxBR,UAAU,CAAC1B,cAAc,CAACY;kBAAI;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACbxD,OAAA,CAAC1C,UAAU;oBAAC6F,OAAO,EAAC,SAAS;oBAACM,KAAK,EAAC,gBAAgB;oBAAAZ,QAAA,EACjDR,UAAU,CAAC1B,cAAc,CAACgB;kBAAI;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA,eACb,CAAC,gBAEHxD,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,OAAO;kBAACM,KAAK,EAAC,gBAAgB;kBAACO,SAAS,EAAC,QAAQ;kBAAAnB,QAAA,EAAC;gBAEtE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNxD,OAAA,CAAC5B,GAAG;gBAAAyE,QAAA,gBACF7C,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,OAAO;kBAACY,UAAU,EAAE,GAAI;kBAAAlB,QAAA,EAAC;gBAE7C;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbxD,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,OAAO;kBAAAN,QAAA,EACxBR,UAAU,CAACJ;gBAAY;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxD,OAAA,CAACpC,MAAM;YACL8F,OAAO,EAAEA,CAAA,KAAMvB,sBAAsB,CAACE,UAAU,CAACf,EAAE,CAAE;YACrDmC,KAAK,EAAC,OAAO;YACbE,IAAI,EAAC,OAAO;YACZC,SAAS,eAAE5D,OAAA,CAACV,UAAU;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BZ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC5B;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC,GA7DLnB,UAAU,CAACf,EAAE;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA8DlB,CACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMS,cAAc,GAAGA,CAAA,kBACrBjE,OAAA,CAAClB,IAAI;IAAC8D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,EAC7BhC,WAAW,CAACiC,GAAG,CAAC,CAACT,UAAU,EAAE6B,KAAK,kBACjClE,OAAA,CAAC9C,KAAK,CAAC+C,QAAQ;MAAA4C,QAAA,gBACb7C,OAAA,CAACjB,QAAQ;QAAA8D,QAAA,gBACP7C,OAAA,CAAChB,YAAY;UACXmF,OAAO,eACLnE,OAAA,CAAC1C,UAAU;YAAC6F,OAAO,EAAC,IAAI;YAACP,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAChDR,UAAU,CAACL,IAAI,CAACT;UAAI;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACb;UACDY,SAAS,eACPpE,OAAA,CAAC5B,GAAG;YAACwE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B7C,OAAA,CAAC1C,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAACM,KAAK,EAAC,gBAAgB;cAAAZ,QAAA,GAC/CR,UAAU,CAACL,IAAI,CAACR,KAAK,EAAC,UAAG,EAACa,UAAU,CAACL,IAAI,CAACP,UAAU;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACbxD,OAAA,CAAC1C,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,aACf,EAACR,UAAU,CAAC5B,iBAAiB,CAACc,IAAI,EAAC,IAAE,EAACc,UAAU,CAAC5B,iBAAiB,CAACkB,IAAI,EAAC,GACrF;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZnB,UAAU,CAAC1B,cAAc,iBACxBX,OAAA,CAAC1C,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,UAClB,EAACR,UAAU,CAAC1B,cAAc,CAACY,IAAI,EAAC,IAAE,EAACc,UAAU,CAAC1B,cAAc,CAACgB,IAAI,EAAC,GAC5E;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb,eACDxD,OAAA,CAAC1C,UAAU;cAAC6F,OAAO,EAAC,SAAS;cAACM,KAAK,EAAC,gBAAgB;cAAAZ,QAAA,GAAC,YACzC,EAACR,UAAU,CAACJ,YAAY;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFxD,OAAA,CAACf,uBAAuB;UAAA4D,QAAA,eACtB7C,OAAA,CAACpC,MAAM;YACL8F,OAAO,EAAEA,CAAA,KAAMvB,sBAAsB,CAACE,UAAU,CAACf,EAAE,CAAE;YACrDmC,KAAK,EAAC,OAAO;YACbE,IAAI,EAAC,OAAO;YACZC,SAAS,eAAE5D,OAAA,CAACV,UAAU;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BZ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC5B;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,EACVU,KAAK,GAAGrD,WAAW,CAACwD,MAAM,GAAG,CAAC,iBAAIrE,OAAA,CAACd,OAAO;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA,GAvC3BnB,UAAU,CAACf,EAAE;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwClB,CACjB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACP;EAED,MAAMc,eAAe,GAAGA,CAAA,kBACtBtE,OAAA,CAAChC,cAAc;IAAC4E,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eACzC7C,OAAA,CAACnC,KAAK;MAAAgF,QAAA,gBACJ7C,OAAA,CAAC/B,SAAS;QAAA4E,QAAA,eACR7C,OAAA,CAAC9B,QAAQ;UAAC0E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBACpC7C,OAAA,CAACjC,SAAS;YAAC6E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACzDxD,OAAA,CAACjC,SAAS;YAAC6E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC/DxD,OAAA,CAACjC,SAAS;YAAC6E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAkB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACvExD,OAAA,CAACjC,SAAS;YAAC6E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACpExD,OAAA,CAACjC,SAAS;YAAC6E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAa;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAClExD,OAAA,CAACjC,SAAS;YAAC6E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACZxD,OAAA,CAAClC,SAAS;QAAA+E,QAAA,EACPhC,WAAW,CAACiC,GAAG,CAACT,UAAU,iBACzBrC,OAAA,CAAC9B,QAAQ;UAAqBqG,KAAK;UAAA1B,QAAA,gBACjC7C,OAAA,CAACjC,SAAS;YAAA8E,QAAA,eACR7C,OAAA,CAAC5B,GAAG;cAAAyE,QAAA,gBACF7C,OAAA,CAAC1C,UAAU;gBAAC6F,OAAO,EAAC,OAAO;gBAACY,UAAU,EAAE,GAAI;gBAAAlB,QAAA,EACzCR,UAAU,CAACL,IAAI,CAACT;cAAI;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACbxD,OAAA,CAAC1C,UAAU;gBAAC6F,OAAO,EAAC,SAAS;gBAACM,KAAK,EAAC,gBAAgB;gBAAAZ,QAAA,EACjDR,UAAU,CAACL,IAAI,CAACR;cAAK;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACZxD,OAAA,CAACjC,SAAS;YAAA8E,QAAA,EAAER,UAAU,CAACL,IAAI,CAACP;UAAU;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnDxD,OAAA,CAACjC,SAAS;YAAA8E,QAAA,eACR7C,OAAA,CAAC5B,GAAG;cAAAyE,QAAA,gBACF7C,OAAA,CAAC1C,UAAU;gBAAC6F,OAAO,EAAC,OAAO;gBAACY,UAAU,EAAE,GAAI;gBAAAlB,QAAA,EACzCR,UAAU,CAAC5B,iBAAiB,CAACc;cAAI;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACbxD,OAAA,CAAC1C,UAAU;gBAAC6F,OAAO,EAAC,SAAS;gBAACM,KAAK,EAAC,gBAAgB;gBAAAZ,QAAA,EACjDR,UAAU,CAAC5B,iBAAiB,CAACkB;cAAI;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACZxD,OAAA,CAACjC,SAAS;YAAA8E,QAAA,EACPR,UAAU,CAAC1B,cAAc,gBACxBX,OAAA,CAAC5B,GAAG;cAAAyE,QAAA,gBACF7C,OAAA,CAAC1C,UAAU;gBAAC6F,OAAO,EAAC,OAAO;gBAACY,UAAU,EAAE,GAAI;gBAAAlB,QAAA,EACzCR,UAAU,CAAC1B,cAAc,CAACY;cAAI;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACbxD,OAAA,CAAC1C,UAAU;gBAAC6F,OAAO,EAAC,SAAS;gBAACM,KAAK,EAAC,gBAAgB;gBAAAZ,QAAA,EACjDR,UAAU,CAAC1B,cAAc,CAACgB;cAAI;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAENxD,OAAA,CAAC1C,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAACM,KAAK,EAAC,gBAAgB;cAACO,SAAS,EAAC,QAAQ;cAAAnB,QAAA,EAAC;YAEtE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACZxD,OAAA,CAACjC,SAAS;YAAA8E,QAAA,EAAER,UAAU,CAACJ;UAAY;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChDxD,OAAA,CAACjC,SAAS;YAAA8E,QAAA,eACR7C,OAAA,CAACpC,MAAM;cACL8F,OAAO,EAAEA,CAAA,KAAMvB,sBAAsB,CAACE,UAAU,CAACf,EAAE,CAAE;cACrDmC,KAAK,EAAC,OAAO;cACbE,IAAI,EAAC,OAAO;cACZR,OAAO,EAAC,WAAW;cACnBS,SAAS,eAAE5D,OAAA,CAACV,UAAU;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BZ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC7B;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA,GAlDCnB,UAAU,CAACf,EAAE;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmDlB,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CACjB;EAED,oBACExD,OAAA,CAAC5C,SAAS;IAACoH,QAAQ,EAAC,IAAI;IAAC5B,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAErD7C,OAAA,CAAC5B,GAAG;MAACwE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB7C,OAAA,CAAC1C,UAAU;QACT6F,OAAO,EAAC,IAAI;QACZsB,SAAS,EAAC,IAAI;QACdrB,YAAY;QACZR,SAAS,EAAC,YAAY;QAAAC,QAAA,EACvB;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxD,OAAA,CAAC1C,UAAU;QACT6F,OAAO,EAAC,IAAI;QACZM,KAAK,EAAC,gBAAgB;QACtBb,SAAS,EAAC,UAAU;QAAAC,QAAA,EACrB;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGLzC,YAAY,CAACG,OAAO,iBACnBlB,OAAA,CAACzB,KAAK;MACJmG,QAAQ,EAAE3D,YAAY,CAACE,IAAK;MAC5B0D,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAE;MAC1D0D,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAhC,QAAA,EAEb9B,YAAY,CAACG;IAAO;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACR,eAEDxD,OAAA,CAAC7B,IAAI;MAACuE,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAE,QAAA,gBAEzB7C,OAAA,CAAC7B,IAAI;QAAC4E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAL,QAAA,eACvB7C,OAAA,CAAC3C,KAAK;UAACyG,SAAS,EAAE,CAAE;UAAClB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzC7C,OAAA,CAAC1C,UAAU;YACT6F,OAAO,EAAC,IAAI;YACZC,YAAY;YACZR,SAAS,EAAC,YAAY;YAAAC,QAAA,EACvB;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbxD,OAAA,CAAC5B,GAAG;YAACqG,SAAS,EAAC,MAAM;YAAC7B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACxC7C,OAAA,CAACxB,YAAY;cACXsG,OAAO,EAAEzD,cAAe;cACxB0D,cAAc,EAAGC,MAAM,IAAKA,MAAM,CAACzD,IAAK;cACxC0D,KAAK,EAAE1E,YAAa;cACpB2E,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAK5E,eAAe,CAAC4E,QAAQ,CAAE;cACrDC,WAAW,EAAGC,MAAM,iBAClBtF,OAAA,CAACzC,SAAS;gBAAA,GACJ+H,MAAM;gBACVC,KAAK,EAAC,aAAa;gBACnBC,MAAM,EAAC,QAAQ;gBACfC,SAAS;gBACTb,EAAE,EAAE;kBACF,uBAAuB,EAAE;oBACvBc,UAAU,EAAE;kBACd,CAAC;kBACD,uBAAuB,EAAE;oBACvBA,UAAU,EAAE;kBACd;gBACF;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACD;cACFZ,SAAS,EAAC;YAAmB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAEFxD,OAAA,CAACxB,YAAY;cACXsG,OAAO,EAAEpD,kBAAmB;cAC5BqD,cAAc,EAAGC,MAAM,IAAK,GAAGA,MAAM,CAACzD,IAAI,MAAMyD,MAAM,CAACrD,IAAI,EAAG;cAC9DsD,KAAK,EAAExE,iBAAkB;cACzByE,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAK1E,oBAAoB,CAAC0E,QAAQ,CAAE;cAC1DC,WAAW,EAAGC,MAAM,iBAClBtF,OAAA,CAACzC,SAAS;gBAAA,GACJ+H,MAAM;gBACVC,KAAK,EAAC,sBAAsB;gBAC5BC,MAAM,EAAC,QAAQ;gBACfC,SAAS;gBACTE,QAAQ;gBACRf,EAAE,EAAE;kBACF,uBAAuB,EAAE;oBACvBc,UAAU,EAAE;kBACd,CAAC;kBACD,uBAAuB,EAAE;oBACvBA,UAAU,EAAE;kBACd;gBACF;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACD;cACFZ,SAAS,EAAC;YAAmB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAEFxD,OAAA,CAACxB,YAAY;cACXsG,OAAO,EAAEpD,kBAAkB,CAACU,MAAM,CAACwD,QAAQ,IAAIA,QAAQ,CAACtE,EAAE,MAAKb,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEa,EAAE,EAAE;cACtFyD,cAAc,EAAGC,MAAM,IAAK,GAAGA,MAAM,CAACzD,IAAI,MAAMyD,MAAM,CAACrD,IAAI,EAAG;cAC9DsD,KAAK,EAAEtE,cAAe;cACtBuE,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAKxE,iBAAiB,CAACwE,QAAQ,CAAE;cACvDC,WAAW,EAAGC,MAAM,iBAClBtF,OAAA,CAACzC,SAAS;gBAAA,GACJ+H,MAAM;gBACVC,KAAK,EAAC,4BAA4B;gBAClCC,MAAM,EAAC,QAAQ;gBACfC,SAAS;gBACTb,EAAE,EAAE;kBACF,uBAAuB,EAAE;oBACvBc,UAAU,EAAE;kBACd,CAAC;kBACD,uBAAuB,EAAE;oBACvBA,UAAU,EAAE;kBACd;gBACF;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACD;cACFZ,SAAS,EAAC;YAAmB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAEFxD,OAAA,CAACpC,MAAM;cACL6H,SAAS;cACTtC,OAAO,EAAC,WAAW;cACnBQ,IAAI,EAAC,OAAO;cACZD,OAAO,EAAE9B,qBAAsB;cAC/BgC,SAAS,eAAE5D,OAAA,CAACZ,aAAa;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC7BZ,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC1B;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPxD,OAAA,CAAC7B,IAAI;QAAC4E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAL,QAAA,eACvB7C,OAAA,CAAC3C,KAAK;UAACyG,SAAS,EAAE,CAAE;UAACc,EAAE,EAAE;YAAEiB,CAAC,EAAE;UAAE,CAAE;UAAAhD,QAAA,gBAChC7C,OAAA,CAAC5B,GAAG;YAACwG,EAAE,EAAE;cAAEkB,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE,QAAQ;cAAEnB,EAAE,EAAE;YAAE,CAAE;YAAAhC,QAAA,gBACzF7C,OAAA,CAAC1C,UAAU;cACT6F,OAAO,EAAC,IAAI;cACZyB,EAAE,EAAE;gBACFqB,YAAY,EAAE,CAAC;gBACfC,WAAW,EAAE,SAAS;gBACtBC,EAAE,EAAE,CAAC;gBACLT,UAAU,EAAE,qBAAqB;gBACjC3B,UAAU,EAAE,GAAG;gBACfN,KAAK,EAAE;cACT,CAAE;cAAAZ,QAAA,GACH,uBACsB,EAAChC,WAAW,CAACwD,MAAM,EAAC,GAC3C;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbxD,OAAA,CAACnB,iBAAiB;cAChBoG,KAAK,EAAE9D,QAAS;cAChBiF,SAAS;cACTlB,QAAQ,EAAE5C,gBAAiB;cAC3BqB,IAAI,EAAC,OAAO;cACZiB,EAAE,EAAE;gBACF,yBAAyB,EAAE;kBACzBc,UAAU,EAAE,qBAAqB;kBACjCW,MAAM,EAAE,mBAAmB;kBAC3B5C,KAAK,EAAE,SAAS;kBAChBqC,OAAO,EAAE,MAAM;kBACfQ,aAAa,EAAE,KAAK;kBACpBN,UAAU,EAAE,QAAQ;kBACpBO,GAAG,EAAE,CAAC;kBACNC,aAAa,EAAE,MAAM;kBACrBC,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,CAAC;kBACL,gBAAgB,EAAE;oBAChBC,eAAe,EAAE,SAAS;oBAC1BlD,KAAK,EAAE,OAAO;oBACd,SAAS,EAAE;sBACTkD,eAAe,EAAE;oBACnB;kBACF,CAAC;kBACD,SAAS,EAAE;oBACTA,eAAe,EAAE;kBACnB;gBACF;cACF,CAAE;cAAA9D,QAAA,gBAEF7C,OAAA,CAACpB,YAAY;gBAACqG,KAAK,EAAC,OAAO;gBAAC2B,KAAK,EAAC,YAAY;gBAAA/D,QAAA,gBAC5C7C,OAAA,CAACF,aAAa;kBAAC+G,QAAQ,EAAC;gBAAO;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClCxD,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,SAAS;kBAACyB,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAI,CAAE;kBAAAjE,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACfxD,OAAA,CAACpB,YAAY;gBAACqG,KAAK,EAAC,MAAM;gBAAC2B,KAAK,EAAC,WAAW;gBAAA/D,QAAA,gBAC1C7C,OAAA,CAACR,YAAY;kBAACqH,QAAQ,EAAC;gBAAO;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjCxD,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,SAAS;kBAACyB,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAI,CAAE;kBAAAjE,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACfxD,OAAA,CAACpB,YAAY;gBAACqG,KAAK,EAAC,MAAM;gBAAC2B,KAAK,EAAC,WAAW;gBAAA/D,QAAA,gBAC1C7C,OAAA,CAACJ,YAAY;kBAACiH,QAAQ,EAAC;gBAAO;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjCxD,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,SAAS;kBAACyB,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAI,CAAE;kBAAAjE,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACfxD,OAAA,CAACpB,YAAY;gBAACqG,KAAK,EAAC,MAAM;gBAAC2B,KAAK,EAAC,WAAW;gBAAA/D,QAAA,gBAC1C7C,OAAA,CAACN,YAAY;kBAACmH,QAAQ,EAAC;gBAAO;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjCxD,OAAA,CAAC1C,UAAU;kBAAC6F,OAAO,EAAC,SAAS;kBAACyB,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAI,CAAE;kBAAAjE,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,EAEL3C,WAAW,CAACwD,MAAM,KAAK,CAAC,gBACvBrE,OAAA,CAAC5B,GAAG;YAAC2I,SAAS,EAAC,QAAQ;YAACL,EAAE,EAAE,CAAE;YAAA7D,QAAA,eAC5B7C,OAAA,CAAC1C,UAAU;cAAC6F,OAAO,EAAC,IAAI;cAACM,KAAK,EAAC,gBAAgB;cAAAZ,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAENxD,OAAA,CAAC5B,GAAG;YAAAyE,QAAA,GACD1B,QAAQ,KAAK,OAAO,IAAImD,eAAe,CAAC,CAAC,EACzCnD,QAAQ,KAAK,MAAM,IAAIsB,cAAc,CAAC,CAAC,EACvCtB,QAAQ,KAAK,MAAM,IAAI0C,cAAc,CAAC,CAAC,EACvC1C,QAAQ,KAAK,MAAM,IAAI8C,cAAc,CAAC,CAAC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACpD,EAAA,CA9gBID,kBAAkB;AAAA6G,EAAA,GAAlB7G,kBAAkB;AAghBxB,eAAeA,kBAAkB;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}