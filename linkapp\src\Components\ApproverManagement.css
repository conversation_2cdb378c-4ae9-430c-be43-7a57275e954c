/* ApproverManagement.css */

/* Container Styles */
.approver-container {
  padding: 32px 0;
}

/* Header Styles */
.header-box {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  font-weight: 600 !important;
  color: #2EC0CB !important;
  font-family: "Inter", sans-serif !important;
  margin-bottom: 16px !important;
}

.subtitle {
  max-width: 600px;
  margin: 0 auto;
  font-family: "Inter", sans-serif !important;
}

/* Grid View Styles */
.grid-container {
  margin-top: 16px;
}

.grid-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.grid-card-content .user-name {
  font-family: "Inter", sans-serif !important;
  margin-bottom: 8px !important;
}

.grid-card-content .associate-text {
  margin-top: 16px;
  font-weight: 500;
}

.grid-card-content .backup-text {
  margin-top: 8px;
  font-weight: 500;
}

.grid-card-content .assigned-date {
  display: block;
  margin-top: 16px;
}

.grid-delete-btn {
  font-family: "Inter", sans-serif !important;
  text-transform: none !important;
}

/* Card View Styles */
.card-container {
  margin-top: 16px;
}

.card-item {
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-content-flex {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.card-content-main {
  flex: 1;
}

.card-user-name {
  font-family: "Inter", sans-serif !important;
  margin-bottom: 8px !important;
}

.card-approvers-box {
  display: flex;
  gap: 32px;
  margin-top: 16px;
}

.card-delete-btn {
  font-family: "Inter", sans-serif !important;
  text-transform: none !important;
}

/* List View Styles */
.list-container {
  margin-top: 16px;
}

.list-user-name {
  font-family: "Inter", sans-serif !important;
}

.list-user-info {
  margin-top: 8px;
}

.list-delete-btn {
  font-family: "Inter", sans-serif !important;
  text-transform: none !important;
}

/* Table View Styles */
.table-container {
  margin-top: 24px;
}

.table-header-row {
  background-color: #f8f9fa !important;
}

.table-header-cell {
  font-weight: 600 !important;
}

.table-delete-btn {
  font-family: "Inter", sans-serif !important;
  font-weight: 500 !important;
  text-transform: none !important;
}

/* Form Section Styles */
.form-paper {
  padding: 24px;
}

.form-title {
  border-bottom: 2px solid #2EC0CB;
  padding-bottom: 8px;
  font-family: "Inter", sans-serif !important;
  font-weight: 600 !important;
  color: #2EC0CB !important;
}

.form-box {
  margin-top: 24px;
}

.form-autocomplete {
  margin-bottom: 16px;
}

.form-input-label {
  font-family: "Inter", sans-serif !important;
}

.form-input-base {
  font-family: "Inter", sans-serif !important;
}

.assign-button {
  margin-top: 24px !important;
  padding: 12px 0 !important;
  font-family: "Inter", sans-serif !important;
  font-weight: 500 !important;
  background-color: #2EC0CB !important;
}

.assign-button:hover {
  background-color: #208B94 !important;
}

/* Assignments Section Styles */
.assignments-paper {
  padding: 24px;
}

.assignments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.assignments-title {
  border-bottom: 2px solid #2EC0CB;
  padding-bottom: 8px;
  font-family: "Inter", sans-serif !important;
  font-weight: 600 !important;
  color: #2EC0CB !important;
}

/* Toggle Button Group Styles */
.view-toggle-group .MuiToggleButton-root {
  font-family: "Inter", sans-serif !important;
  border: 1px solid #2EC0CB !important;
  color: #2EC0CB !important;
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 8px !important;
  text-transform: none !important;
  padding: 8px 16px !important;
}

.view-toggle-group .MuiToggleButton-root.Mui-selected {
  background-color: #2EC0CB !important;
  color: white !important;
}

.view-toggle-group .MuiToggleButton-root.Mui-selected:hover {
  background-color: #208B94 !important;
}

.view-toggle-group .MuiToggleButton-root:hover {
  background-color: rgba(46, 192, 203, 0.1) !important;
}

/* Empty State Styles */
.empty-state {
  text-align: center;
  padding: 48px 0;
}

/* Notification Styles */
.notification-alert {
  margin-bottom: 24px !important;
}

/* Toggle Button Text Styles */
.toggle-button-text {
  margin-left: 4px !important;
}

/* TextField Input Styles */
.form-textfield .MuiInputLabel-root {
  font-family: "Inter", sans-serif !important;
}

.form-textfield .MuiInputBase-input {
  font-family: "Inter", sans-serif !important;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
}

.header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 30px;
  align-items: start;
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

/* Assignment Form Styles */
.assignment-form {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  border: 1px solid #e9ecef;
  height: fit-content;
}

.assignment-form h2 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #34495e;
  font-size: 0.95rem;
}

.form-select,
.search-input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: white;
}

.form-select:focus,
.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-input::placeholder {
  color: #95a5a6;
}

.assign-btn {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.assign-btn:hover {
  background: linear-gradient(135deg, #2980b9, #21618c);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(52, 152, 219, 0.3);
}

.assign-btn:active {
  transform: translateY(0);
}

/* Assignments List Styles */
.assignments-list {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  border: 1px solid #e9ecef;
}

.assignments-list h2 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #27ae60;
  padding-bottom: 10px;
}

.no-assignments {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.no-assignments p {
  font-size: 1.1rem;
  margin: 0;
}

/* Table Styles */
.assignments-table {
  overflow-x: auto;
}

.assignments-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.assignments-table th,
.assignments-table td {
  padding: 15px 12px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.assignments-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.assignments-table tr:hover {
  background-color: #f8f9fa;
}

.user-info,
.approver-info {
  line-height: 1.4;
}

.user-info strong,
.approver-info strong {
  color: #2c3e50;
  font-weight: 600;
}

.user-info small,
.approver-info small {
  color: #7f8c8d;
  font-size: 0.85rem;
}

.no-backup {
  color: #95a5a6;
  font-style: italic;
  font-size: 0.9rem;
}

/* Status Select Styles */
.status-select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: white;
  cursor: pointer;
}

.status-select.active {
  background-color: #d4edda;
  border-color: #27ae60;
  color: #27ae60;
}

.status-select.inactive {
  background-color: #f8d7da;
  border-color: #e74c3c;
  color: #e74c3c;
}

.status-select.pending {
  background-color: #fff3cd;
  border-color: #f39c12;
  color: #f39c12;
}

/* Remove Button Styles */
.remove-btn {
  padding: 8px 16px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background-color: #c0392b;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(231, 76, 60, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .approver-management {
    padding: 15px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .assignment-form,
  .assignments-list {
    padding: 20px;
  }
  
  .assignments-table {
    font-size: 0.9rem;
  }
  
  .assignments-table th,
  .assignments-table td {
    padding: 10px 8px;
  }
  
  .main-content {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.8rem;
  }
  
  .assignments-table th,
  .assignments-table td {
    padding: 8px 6px;
    font-size: 0.85rem;
  }
  
  .assign-btn {
    padding: 12px;
    font-size: 1rem;
  }
}

/* Loading and Animation Effects */
.assignment-form,
.assignments-list {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus and Accessibility */
.form-select:focus,
.search-input:focus,
.assign-btn:focus,
.remove-btn:focus,
.status-select:focus {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* Success and Error States */
.form-group.error .form-select,
.form-group.error .search-input {
  border-color: #e74c3c;
  background-color: #fdf2f2;
}

.form-group.success .form-select,
.form-group.success .search-input {
  border-color: #27ae60;
  background-color: #f2fdf4;
}
